import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots as ms

def my_round(val, digit=0):
    p = 10 ** digit
    rounded = (val * p * 2 + 1) // 2 / p
    return int(rounded) if digit == 0 else rounded

def modified_df(df):
    columns = [
        '需求 (W)', '太阳能发电量 (W)', '商用电源费用 (元/W)', '太阳能售电价格 (元/W)',
        '太阳能使用量 (W)', '太阳能充电量 (W)', '太阳能售电量 (W)', '蓄电使用量 (W)',
        '商用电源使用量 (W)', '商用电源充电量 (W)', '剩余蓄电量 (W)']
    df2 = df.T
    df2.columns = columns
    return df2

def plotly_demand_graph(df):
    '''
    input: modified_df
    output: plotly fig
    '''
    fig = ms(rows=1, cols=1, specs=[[{'secondary_y': True}]])
    fig.add_bar(
        x=df.index, y=df['需求 (W)'], name='需求 (W)',
        offsetgroup='left', marker=dict(color='gray'))
    fig.add_bar(
        x=df.index, y=df['太阳能使用量 (W)'], name='太阳能使用量 (W)',
        offsetgroup='right', marker=dict(color='coral'))
    fig.add_bar(
        x=df.index, y=df['蓄电使用量 (W)'], name='蓄电使用量 (W)',
        offsetgroup='right', base=df['太阳能使用量 (W)'],
        marker=dict(color='deepskyblue'))
    fig.add_bar(
        x=df.index, y=df['商用电源使用量 (W)'], name='商用电源使用量 (W)',
        offsetgroup='right', base=df['太阳能使用量 (W)'] + df['蓄电使用量 (W)'],
        marker=dict(color='limegreen'))
    
    trace0 = go.Scatter(
        x=df.index, y=df['商用电源费用 (元/W)'], name='商用电源费用 (元/W)',
        line=dict(color='green'))
    fig.add_trace(trace0, secondary_y=True)
    trace1 = go.Scatter(
        x=df.index, y=df['太阳能售电价格 (元/W)'], name='太阳能售电价格 (元/W)',
        line=dict(color='orange'))
    fig.add_trace(trace1, secondary_y=True)

    fig.update_xaxes(title_text='时间', showgrid=False)
    fig.update_yaxes(title_text='电力量 (W)', showgrid=False)
    fig.update_yaxes(title_text='商用电源价格 (元/W)', showgrid=False, secondary_y=True)
    fig.update_layout(title='需求与供应')
    return fig

def plotly_solar_graph(df):
    '''
    input: modified_df
    output: plotly fig
    '''
    fig = ms(rows=1, cols=1, specs=[[{'secondary_y': True}]])
    fig.add_bar(
        x=df.index, y=df['太阳能发电量 (W)'], name='太阳能发电量 (W)',
        offsetgroup='left', marker=dict(color='lightgray'))
    fig.add_bar(
        x=df.index, y=df['太阳能使用量 (W)'], name='太阳能使用量 (W)',
        offsetgroup='right', marker=dict(color='coral'))
    fig.add_bar(
        x=df.index, y=df['太阳能充电量 (W)'], name='太阳能充电量 (W)',
        offsetgroup='right', base=df['太阳能使用量 (W)'],
        marker=dict(color='gold'))
    fig.add_bar(
        x=df.index, y=df['太阳能售电量 (W)'], name='太阳能售电量 (W)',
        offsetgroup='right', base=df['太阳能使用量 (W)'] + df['太阳能充电量 (W)'],
        marker=dict(color='aqua'))

    trace0 = go.Scatter(
        x=df.index, y=df['商用电源费用 (元/W)'], name='商用电源费用 (元/W)',
        line=dict(color='green'))
    fig.add_trace(trace0, secondary_y=True)
    trace1 = go.Scatter(
        x=df.index, y=df['太阳能售电价格 (元/W)'], name='太阳能售电价格 (元/W)',
        line=dict(color='orange'))
    fig.add_trace(trace1, secondary_y=True)

    fig.update_xaxes(title_text='时间', showgrid=False)
    fig.update_yaxes(title_text='电力量 (W)', showgrid=False)
    fig.update_yaxes(title_text='太阳能售电价格 (元/W)', showgrid=False, secondary_y=True)
    fig.update_layout(title='太阳能收支')
    return fig

def plotly_charge_graph(df):
    '''
    input: modified_df
    output: plotly fig
    '''
    fig = ms(rows=1, cols=1, specs=[[{'secondary_y': True}]])
    fig.add_bar(
        x=df.index, y=df['太阳能充电量 (W)'], name='太阳能充电量 (W)',
        offsetgroup='left', marker=dict(color='lightgray'))
    fig.add_bar(
        x=df.index, y=df['商用电源充电量 (W)'], name='商用电源充电量 (W)',
        offsetgroup='left', base=df['太阳能充电量 (W)'],
        marker=dict(color='coral'))
    fig.add_bar(
        x=df.index, y=df['蓄电使用量 (W)'], name='蓄电使用量 (W)',
        offsetgroup='mid', marker=dict(color='gold'))
    fig.add_bar(
        x=df.index, y=df['剩余蓄电量 (W)'], name='剩余蓄电量 (W)',
        offsetgroup='right', marker=dict(color='brown'))

    trace0 = go.Scatter(
        x=df.index, y=df['商用电源费用 (元/W)'], name='商用电源费用 (元/W)',
        line=dict(color='green'))
    fig.add_trace(trace0, secondary_y=True)
    trace1 = go.Scatter(
        x=df.index, y=df['太阳能售电价格 (元/W)'], name='太阳能售电价格 (元/W)',
        line=dict(color='orange'))
    fig.add_trace(trace1, secondary_y=True)

    fig.update_xaxes(title_text='时间', showgrid=False)
    fig.update_yaxes(title_text='电力量 (W)', showgrid=False)
    fig.update_yaxes(title_text='太阳能售电价格 (元/W)', showgrid=False, secondary_y=True)
    fig.update_layout(title='蓄电池相关')
    return fig

def plotly_bat_compare(df1, df2):
    '''
    input: modified_df1, modified_df2
    output: plotly fig
    '''
    fig = ms(rows=1, cols=1, specs=[[{'secondary_y': True}]])
    fig.add_bar(
        x=df1.index, y=df1['剩余蓄电量 (W)'], name='剩余蓄电量 (初始蓄电量 4500W)',
        offsetgroup='left', marker=dict(color='firebrick'))
    fig.add_bar(
        x=df1.index, y=df1['蓄电使用量 (W)'], name='蓄电使用量 (初始蓄电量 4500W)',
        offsetgroup='left', base=df1['剩余蓄电量 (W)'],
        marker=dict(color='lightcoral'))
    fig.add_bar(
        x=df2.index, y=df2['剩余蓄电量 (W)'], name='剩余蓄电量 (初始蓄电量 0W)',
        offsetgroup='right', marker=dict(color='darkblue'))
    fig.add_bar(
        x=df2.index, y=df2['蓄电使用量 (W)'], name='蓄电使用量 (初始蓄电量 0W)',
        offsetgroup='right', base=df2['剩余蓄电量 (W)'],
        marker=dict(color='dodgerblue'))

    fig.update_xaxes(title_text='时间', showgrid=False)
    fig.update_yaxes(title_text='电力量 (W)', showgrid=False)
    fig.update_layout(title='初始蓄电量对蓄电池剩余量的影响')
    # 参考：https://classynode.com/2021/08/plotly_legend_layout/
    fig.update_layout(
        legend=dict(
            x=0.99,          # ①：X座標
            y=0.99,          # ①：Y座標
            xanchor='right',  # ②：X座標が凡例のどの部分を表すか
            yanchor='top',   # ②：Y座標が凡例のどの部分を表すか
            # orientation='h', # ③：凡例を横並びにする
    ))
    return fig

def plotly_demand_compare(df1, df2):
    '''
    input: modified_df1, modified_df2
    output: plotly fig
    '''
    fig = ms(rows=1, cols=1, specs=[[{'secondary_y': True}]])
    # 需求
    fig.add_bar(
        x=df1.index, y=df1['需求 (W)'], name='需求 (W)',
        offsetgroup='left', marker=dict(color='gray'))
    # df1
    fig.add_bar(
        x=df1.index, y=df1['太阳能使用量 (W)'], name='太阳能使用量 (初始蓄电量 4500W)',
        offsetgroup='mid', marker=dict(color='coral'))
    fig.add_bar(
        x=df1.index, y=df1['蓄电使用量 (W)'], name='蓄电使用量 (初始蓄电量 4500W)',
        offsetgroup='mid', base=df1['太阳能使用量 (W)'],
        marker=dict(color='deepskyblue'))
    fig.add_bar(
        x=df1.index, y=df1['商用电源使用量 (W)'], name='商用电源使用量 (初始蓄电量 4500W)',
        offsetgroup='mid', base=df1['太阳能使用量 (W)'] + df1['蓄电使用量 (W)'],
        marker=dict(color='limegreen'))
    # df2
    fig.add_bar(
        x=df2.index, y=df2['太阳能使用量 (W)'], name='太阳能使用量 (初始蓄电量 0W)',
        offsetgroup='right', marker=dict(color='peachpuff'))
    fig.add_bar(
        x=df2.index, y=df2['蓄电使用量 (W)'], name='蓄电使用量 (初始蓄电量 0W)',
        offsetgroup='right', base=df2['太阳能使用量 (W)'],
        marker=dict(color='skyblue'))
    fig.add_bar(
        x=df2.index, y=df2['商用电源使用量 (W)'], name='商用电源使用量 (初始蓄电量 0W)',
        offsetgroup='right', base=df2['太阳能使用量 (W)'] + df2['蓄电使用量 (W)'],
        marker=dict(color='lightgreen'))

    trace0 = go.Scatter(
        x=df1.index, y=df1['商用电源费用 (元/W)'], name='商用电源费用 (元/W)',
        line=dict(color='green'))
    fig.add_trace(trace0, secondary_y=True)
    trace1 = go.Scatter(
        x=df1.index, y=df1['太阳能售电价格 (元/W)'], name='太阳能售电价格 (元/W)',
        line=dict(color='orange'))
    fig.add_trace(trace1, secondary_y=True)

    fig.update_xaxes(title_text='时间', showgrid=False)
    fig.update_yaxes(title_text='电力量 (W)', showgrid=False)
    fig.update_yaxes(title_text='商用电源价格 (元/W)', showgrid=False, secondary_y=True)
    fig.update_layout(title='初始蓄电量对供应的影响')
    fig.update_layout(showlegend=False)
    return fig
