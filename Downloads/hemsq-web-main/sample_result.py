import pandas as pd


def make_df(lis2d):
    return pd.DataFrame(
        lis2d,
        index=[
            '需要 (W)', '太陽光発電量 (W)', '商用電源料金 (円/W)', '太陽光売電価格 (円/W)',
            '太陽光使用量 (W)', '太陽光充電量 (W)', '太陽光売上量 (W)', '蓄電使用量 (W)',
            '商用電源使用量 (W)', '商用電源充電量 (W)', '残りの蓄電量 (W)'],
        columns=['{}:00'.format(i) for i in range(24)],
    ).T

result_bat4500 = {
    'df': make_df(
        [
            [207, 177, 147, 157, 157, 167, 228, 330, 381, 391, 351, 311, 341, 341, 311, 310, 320, 331, 372, 542, 549, 509, 438, 318],
            [0, 0, 0, 0, 0, 0, 0, 40, 120, 200, 240, 280, 280, 280, 240, 200, 160, 80, 0, 0, 0, 0, 0, 0],
            [0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.026, 0.026, 0.026, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026],
            [0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008],
            [0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 200, 100, 100, 280, 200, 200, 0, 80, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, 0, 0, 0, 0, 0, 160, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 40, 20, 0, 40, 180, 180, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [200, 177, 100, 0, 157, 0, 0, 300, 281, 0, 151, 200, 200, 0, 100, 100, 0, 200, 0, 400, 300, 500, 0, 0],
            [7, 0, 47, 157, 0, 167, 228, 30, 0, 391, 0, 11, 41, 61, 11, 10, 320, 51, 372, 142, 249, 9, 438, 318],
            [0, 0, 0, 300, 0, 600, 500, 0, 0, 100, 0, 0, 0, 100, 0, 0, 100, 0, 0, 0, 0, 0, 100, 0],
            [4075, 3648, 3387, 3518, 3099, 3585, 3919, 3423, 2933, 3105, 2700, 2412, 2079, 2076, 1872, 1678, 1854, 1599, 1520, 1044, 691, 157, 249, 236],
        ],
    ),
    'cost': 102.8,
    'CO2': 2.2,
}

result_bat0 = {
    'df': make_df(
        [
            [207, 177, 147, 157, 157, 167, 228, 330, 381, 391, 351, 311, 341, 341, 311, 310, 320, 331, 372, 542, 549, 509, 438, 318],
            [0, 0, 0, 0, 0, 0, 0, 40, 120, 200, 240, 280, 280, 280, 240, 200, 160, 80, 0, 0, 0, 0, 0, 0],
            [0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.026, 0.026, 0.026, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026],
            [0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008],
            [0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 200, 0, 200, 200, 200, 100, 160, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, 280, 80, 80, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 40, 20, 0, 40, 0, 0, 0, 40, 100, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 281, 0, 151, 0, 0, 0, 0, 200, 100, 0, 0, 500, 500, 500, 0, 0],
            [207, 177, 147, 157, 157, 167, 228, 330, 0, 391, 0, 311, 141, 141, 111, 10, 60, 331, 372, 42, 49, 9, 438, 318],
            [300, 0, 0, 0, 0, 900, 800, 300, 0, 100, 0, 100, 0, 0, 100, 0, 0, 0, 500, 0, 0, 0, 0, 0],
            [300, 284, 270, 257, 244, 1132, 1845, 2052, 1631, 1867, 1525, 1875, 1885, 1889, 1914, 1618, 1437, 1445, 1925, 1328, 762, 224, 212, 202],
        ],
    ),
    'cost': 157.6,
    'CO2': 3.3,
}

result_cost10env0 = {
    'df': make_df(
        [
            [207, 177, 147, 157, 157, 167, 228, 330, 381, 391, 351, 311, 341, 341, 311, 310, 320, 331, 372, 542, 549, 509, 438, 318],
            [0, 0, 0, 0, 0, 0, 0, 40, 120, 200, 240, 280, 280, 280, 240, 200, 160, 80, 0, 0, 0, 0, 0, 0],
            [0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.026, 0.026, 0.026, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026],
            [0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008],
            [0, 0, 0, 0, 0, 0, 0, 0, 100, 200, 200, 200, 0, 100, 0, 200, 160, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 40, 20, 0, 40, 0, 280, 180, 240, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [200, 177, 0, 0, 0, 167, 0, 300, 281, 191, 151, 0, 300, 200, 300, 100, 100, 0, 0, 200, 400, 300, 400, 0],
            [7, 0, 147, 157, 157, 0, 228, 30, 0, 0, 0, 111, 41, 41, 11, 10, 60, 331, 372, 342, 149, 209, 38, 318],
            [0, 0, 100, 500, 100, 0, 700, 0, 0, 0, 0, 400, 0, 0, 0, 0, 0, 200, 500, 0, 0, 0, 0, 0],
            [4075, 3648, 3587, 3908, 3812, 3389, 3929, 3433, 2942, 2604, 2234, 2648, 2265, 1951, 1554, 1376, 1207, 1427, 1829, 1538, 1061, 708, 273, 259],
        ],
    ),
    'cost': 109.8,
    'CO2': 2.3,
}

result_cost0env10 = {
    'df': make_df(
        [
            [207, 177, 147, 157, 157, 167, 228, 330, 381, 391, 351, 311, 341, 341, 311, 310, 320, 331, 372, 542, 549, 509, 438, 318],
            [0, 0, 0, 0, 0, 0, 0, 40, 120, 200, 240, 280, 280, 280, 240, 200, 160, 80, 0, 0, 0, 0, 0, 0],
            [0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.012, 0.026, 0.026, 0.026, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.039, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026, 0.026],
            [0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008, 0.008],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 200, 200, 200, 100, 100, 100, 80, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, 80, 80, 0, 0, 100, 60, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 40, 120, 0, 40, 0, 0, 80, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [200, 177, 100, 157, 0, 167, 200, 300, 381, 0, 100, 0, 0, 100, 200, 0, 0, 200, 0, 0, 300, 500, 400, 200],
            [7, 0, 47, 0, 157, 0, 28, 30, 0, 391, 51, 111, 141, 41, 11, 210, 220, 51, 372, 542, 249, 9, 38, 118],
            [0, 0, 0, 0, 600, 0, 0, 0, 0, 400, 0, 100, 0, 0, 0, 100, 300, 0, 200, 100, 0, 0, 0, 0],
            [4075, 3648, 3387, 2975, 3467, 3061, 2745, 2307, 1773, 2302, 2087, 2163, 2170, 1980, 1681, 1797, 2067, 1802, 1909, 1914, 1518, 942, 495, 270],
        ],
    ),
    'cost': 121.8,
    'CO2': 2.1,
}
