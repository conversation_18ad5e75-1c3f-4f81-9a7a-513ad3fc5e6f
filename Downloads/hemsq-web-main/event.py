import streamlit as st
import requests
import json

def event():
    st.text_input(
        '您发现得很好！！请点击下面的内容试试',
        placeholder='即将到来的活动必需的食物是？',
        key='special_event',
        on_change=st.session_state.pages['首页'].func)
    if st.session_state.special_event in [
            '巧克力', '🍫', 'chocolate', '朱古力',
            '巧克力糖', '巧克力块']:
        display()
        question()

def display():
    st.write('太棒了！！')
    st.image('https://drive.google.com/uc?export=view&id=1W0TtWXV5O-7emlBPv5OWE3VDBN9Gr1w6&usp=sharing')

def submit():
    options = ', '.join(st.session_state.question)
    text = f'''
＜如何发现的？＞
{options}

＜留言＞
{st.session_state.event_message}
'''
    requests.post('https://hemsq-event.herokuapp.com/event',
        headers={"content-type": "application/json"},
        # data=json.dumps({'name': st.session_state.event_name, 'text': text}))
        data=json.dumps({'name': '匿名用户', 'text': text}))
    st.session_state.pages['首页'].func()

def question():
    st.write('如果方便的话，请回答问卷调查！')
    with st.form('question_form'):
        # name = st.text_input('昵称', placeholder='不填也可以 :)',
        #     key='event_name')
        how = st.multiselect(
            '您是如何发现的？(可多选)',
            options=[
                '随便点击时偶然发现的',
                '使用深色模式时很明显',
                '觉得有什么奇怪的地方所以探索了一下',
                '以上都不是',
            ],
            key='question')
        message = st.text_area('如果能留言的话主人会很开心的',
            placeholder='当然不留言也可以 :)',
            key='event_message')
        st.form_submit_button(
            label='提交',
            on_click=submit,
        )

sukima_text = '...嗯？哦哦哦哦哦？'
